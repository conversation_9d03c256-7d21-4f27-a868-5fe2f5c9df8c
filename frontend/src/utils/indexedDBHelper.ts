/**
 * IndexedDB工具类
 * 用于存储和管理大量的TLE数据
 */

export interface TleDataItem {
  cospar_id: string;
  norad_id: number;
  epoch: string;
  time: string;
  tle_raw: string;
  satellite_name: string;
}

export interface ConstellationInfo {
  id: string;
  name: string;
  description?: string;
  operator?: string;
  country?: string;
  satelliteCount?: number;
  [key: string]: any;
}

export interface ConstellationDataCache {
  id: string; // 固定为 'constellation-cache'
  lastUpdated: string;
  nextUpdate: string;
  total: number;
  data: ConstellationInfo[];
}

export interface TleDataCache {
  id: string; // 固定为 'tle-cache'
  lastUpdated: string; // 最后更新时间 ISO字符串
  nextUpdate: string;  // 下次更新时间 ISO字符串
  total: number;       // 卫星总数
  apiResponse: {       // 保存完整的API响应信息
    success: boolean;
    total: number;
    executionTime: number;
    timeRangeStart: string;
    timeRangeEnd: number;
    timeRangeMinutes: number;
    method: string;
    queryStrategy: string;
    sampleMode: boolean;
  };
  data: TleDataItem[];
}

class IndexedDBHelper {
  private dbName = 'SpaceDataDB';
  private version = 1;
  private storeName = 'tleData';
  private db: IDBDatabase | null = null;
  private initAttempts = 0;
  private readonly MAX_INIT_ATTEMPTS = 3;
  private readonly INIT_TIMEOUT = 30000; // 增加到30秒

  /**
   * 初始化IndexedDB连接
   */
  async init(): Promise<void> {
    console.log('💾 开始 IndexedDB 初始化...');
    
    // 检查浏览器支持
    if (!window.indexedDB) {
      throw new Error('当前浏览器不支持 IndexedDB');
    }

    // 先进行数据库健康检查
    await this.performHealthCheck();
    
    // 检查存储配额
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      try {
        const estimate = await navigator.storage.estimate();
        console.log('💾 存储配额信息:', {
          quota: estimate.quota ? `${(estimate.quota / 1024 / 1024 / 1024).toFixed(2)} GB` : '未知',
          usage: estimate.usage ? `${(estimate.usage / 1024 / 1024).toFixed(2)} MB` : '未知',
          available: estimate.quota && estimate.usage ? 
            `${((estimate.quota - estimate.usage) / 1024 / 1024 / 1024).toFixed(2)} GB` : '未知'
        });

        // 检查可用空间是否足够
        if (estimate.quota && estimate.usage) {
          const availableGB = (estimate.quota - estimate.usage) / 1024 / 1024 / 1024;
          if (availableGB < 0.1) { // 小于100MB
            console.warn('💾 ⚠️ 可用存储空间不足，可能影响数据存储');
          }
        }
      } catch (error) {
        console.warn('💾 无法获取存储配额信息:', error);
      }
    }
    
    console.log('💾 数据库配置:', {
      dbName: this.dbName,
      version: this.version,
      storeName: this.storeName
    });
    
    return this.attemptInitialization();
  }

  /**
   * 执行数据库健康检查
   */
  private async performHealthCheck(): Promise<void> {
    console.log('💾 执行数据库健康检查...');
    
    try {
      // 检查是否存在其他连接阻塞
      const databases = await indexedDB.databases();
      const existingDB = databases.find(db => db.name === this.dbName);
      
      if (existingDB) {
        console.log('💾 发现现有数据库:', existingDB);
        
        // 尝试快速连接测试
        const testResult = await this.performQuickConnectionTest();
        if (!testResult) {
          console.warn('💾 数据库连接测试失败，可能需要重建数据库');
          await this.resetDatabase();
        }
      }
    } catch (error) {
      console.warn('💾 健康检查失败:', error);
    }
  }

  /**
   * 快速连接测试
   */
  private performQuickConnectionTest(): Promise<boolean> {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        console.warn('💾 快速连接测试超时');
        resolve(false);
      }, 5000);

      try {
        const request = indexedDB.open(this.dbName, this.version);
        
        request.onsuccess = () => {
          clearTimeout(timeout);
          request.result.close();
          console.log('💾 快速连接测试成功');
          resolve(true);
        };
        
        request.onerror = () => {
          clearTimeout(timeout);
          console.warn('💾 快速连接测试失败');
          resolve(false);
        };
        
        request.onblocked = () => {
          clearTimeout(timeout);
          console.warn('💾 快速连接测试被阻塞');
          resolve(false);
        };
      } catch (error) {
        clearTimeout(timeout);
        console.warn('💾 快速连接测试异常:', error);
        resolve(false);
      }
    });
  }

  /**
   * 重置数据库
   */
  private async resetDatabase(): Promise<void> {
    console.log('💾 开始重置数据库...');
    
    try {
      // 关闭现有连接
      if (this.db) {
        this.db.close();
        this.db = null;
      }

      // 删除数据库
      await new Promise<void>((resolve, reject) => {
        const deleteRequest = indexedDB.deleteDatabase(this.dbName);
        
        deleteRequest.onsuccess = () => {
          console.log('💾 数据库删除成功');
          resolve();
        };
        
        deleteRequest.onerror = () => {
          console.error('💾 数据库删除失败:', deleteRequest.error);
          reject(deleteRequest.error);
        };
        
        deleteRequest.onblocked = () => {
          console.warn('💾 数据库删除被阻塞，请关闭其他标签页');
          // 继续尝试，不阻塞流程
          setTimeout(() => resolve(), 2000);
        };
      });

      console.log('💾 数据库重置完成');
    } catch (error) {
      console.error('💾 数据库重置失败:', error);
      throw error;
    }
  }

  /**
   * 尝试初始化数据库
   */
  private attemptInitialization(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.initAttempts++;
      console.log(`💾 第 ${this.initAttempts} 次初始化尝试`);

      // 增加超时时间到30秒，并根据尝试次数递增
      const timeoutDuration = this.INIT_TIMEOUT + (this.initAttempts - 1) * 10000;
      
      const timeoutId = setTimeout(async () => {
        console.error(`💾 IndexedDB 初始化超时 (${timeoutDuration/1000}秒)`);
        
        if (this.initAttempts < this.MAX_INIT_ATTEMPTS) {
          console.log('💾 尝试重置数据库后重新初始化...');
          try {
            await this.resetDatabase();
            // 递归重试
            this.attemptInitialization().then(resolve).catch(reject);
            return;
          } catch (resetError) {
            console.error('💾 重置数据库失败:', resetError);
          }
        }
        
        reject(new Error(`IndexedDB 初始化超时 (尝试了 ${this.initAttempts} 次)`));
      }, timeoutDuration);
      
      const cleanup = () => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
      };
      
      console.log('💾 调用 indexedDB.open...');
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = async () => {
        cleanup();
        console.error('💾 IndexedDB打开失败:', request.error);
        console.error('💾 错误详情:', {
          name: request.error?.name,
          message: request.error?.message,
          code: (request.error as any)?.code
        });
        
        // 根据错误类型提供解决方案
        this.provideErrorSolutions(request.error);
        
        // 如果还有重试机会，尝试重置数据库
        if (this.initAttempts < this.MAX_INIT_ATTEMPTS) {
          console.log('💾 尝试重置数据库后重新初始化...');
          try {
            await this.resetDatabase();
            this.attemptInitialization().then(resolve).catch(reject);
            return;
          } catch (resetError) {
            console.error('💾 重置数据库失败:', resetError);
          }
        }
        
        reject(request.error);
      };

      request.onsuccess = () => {
        cleanup();
        this.db = request.result;
        this.initAttempts = 0; // 重置计数
        
        console.log('💾 IndexedDB连接成功');
        console.log('💾 数据库信息:', {
          name: this.db.name,
          version: this.db.version,
          objectStoreNames: Array.from(this.db.objectStoreNames)
        });
        
        // 添加连接错误处理
        this.db.onerror = (event) => {
          console.error('💾 数据库连接发生错误:', event);
        };
        
        this.db.onversionchange = () => {
          console.warn('💾 数据库版本变化，关闭当前连接');
          this.db?.close();
          this.db = null;
        };
        
        resolve();
      };

      request.onupgradeneeded = (event) => {
        console.log('💾 数据库需要升级，当前版本:', event.oldVersion, '目标版本:', event.newVersion);
        const db = (event.target as IDBOpenDBRequest).result;
        
        try {
          // 创建对象存储
          if (!db.objectStoreNames.contains(this.storeName)) {
            console.log('💾 创建对象存储:', this.storeName);
            const store = db.createObjectStore(this.storeName, { keyPath: 'id' });
            console.log('💾 创建TLE数据存储表');
            
            // 可以在这里添加索引
            // store.createIndex('lastUpdated', 'lastUpdated', { unique: false });
          } else {
            console.log('💾 对象存储已存在:', this.storeName);
          }
        } catch (upgradeError) {
          console.error('💾 数据库升级失败:', upgradeError);
          throw upgradeError;
        }
      };
      
      request.onblocked = (event) => {
        console.warn('💾 IndexedDB 被阻塞，可能有其他标签页正在使用数据库');
        console.warn('💾 建议关闭其他标签页后重试');
        
        // 设置较短的超时，然后尝试强制处理
        setTimeout(() => {
          console.warn('💾 尝试处理阻塞状态...');
        }, 5000);
      };
    });
  }

  /**
   * 提供错误解决方案
   */
  private provideErrorSolutions(error: DOMException | null): void {
    if (!error) return;
    
    console.error('💾 错误解决方案:');
    
    switch (error.name) {
      case 'QuotaExceededError':
        console.error('💾 存储配额不足解决方案:');
        console.error('💾 1. 清理浏览器数据和缓存');
        console.error('💾 2. 关闭不必要的标签页');
        console.error('💾 3. 释放磁盘空间');
        break;
      case 'InvalidStateError':
        console.error('💾 数据库状态错误解决方案:');
        console.error('💾 1. 重启浏览器');
        console.error('💾 2. 清理浏览器数据');
        console.error('💾 3. 检查是否有其他应用占用数据库');
        break;
      case 'VersionError':
        console.error('💾 版本错误解决方案:');
        console.error('💾 1. 清理浏览器数据');
        console.error('💾 2. 重新加载页面');
        break;
      default:
        console.error('💾 通用解决方案:');
        console.error('💾 1. 关闭其他标签页');
        console.error('💾 2. 清理浏览器缓存和数据');
        console.error('💾 3. 重启浏览器');
        console.error('💾 4. 检查磁盘空间');
        break;
    }
  }

  /**
   * 存储TLE数据
   */
  async saveTleData(data: TleDataCache): Promise<void> {
    if (!this.db) {
      console.log('💾 IndexedDB 未初始化，开始初始化...');
      await this.init();
    }

    const dataSize = JSON.stringify(data).length;
    console.log('💾 准备保存数据到 IndexedDB:', {
      dataId: data.id,
      dataSize: `${(dataSize / 1024 / 1024).toFixed(2)} MB`,
      satelliteCount: data.total,
      hasData: !!data.data,
      dataLength: data.data?.length || 0
    });

    // 检查数据大小是否合理
    if (dataSize > 50 * 1024 * 1024) { // 50MB
      console.warn('💾 ⚠️ 数据大小超过50MB，可能影响性能');
    }

    return this.performSaveOperation(data, 1);
  }

  /**
   * 执行保存操作（支持重试）
   */
  private async performSaveOperation(data: TleDataCache, attempt: number): Promise<void> {
    const maxAttempts = 3;
    
    return new Promise(async (resolve, reject) => {
      try {
        console.log(`💾 第 ${attempt} 次保存尝试...`);
        
        // 验证数据库连接状态
        if (!this.db) {
          console.log('💾 数据库连接异常，重新初始化...');
          await this.init();
        }

        console.log('💾 创建 IndexedDB 事务...');
        const transaction = this.db!.transaction([this.storeName], 'readwrite');
        
        // 设置事务超时
        const transactionTimeout = setTimeout(() => {
          console.error('💾 事务超时，尝试中止...');
          try {
            transaction.abort();
          } catch (abortError) {
            console.error('💾 中止事务失败:', abortError);
          }
        }, 60000); // 60秒超时
        
        const cleanup = () => {
          if (transactionTimeout) {
            clearTimeout(transactionTimeout);
          }
        };
        
        transaction.oncomplete = () => {
          cleanup();
          console.log(`💾 IndexedDB 事务完成 (尝试 ${attempt})`);
          resolve();
        };
        
        transaction.onerror = async (event) => {
          cleanup();
          console.error(`💾 IndexedDB 事务失败 (尝试 ${attempt}):`, event);
          console.error('💾 事务错误详情:', {
            error: transaction.error,
            errorName: transaction.error?.name,
            errorMessage: transaction.error?.message
          });
          
          if (attempt < maxAttempts) {
            console.log(`💾 准备第 ${attempt + 1} 次重试...`);
            
            // 特定错误处理
            if (transaction.error?.name === 'QuotaExceededError') {
              console.log('💾 存储配额不足，尝试清理数据...');
              await this.clearOldData();
            } else if (transaction.error?.name === 'InvalidStateError') {
              console.log('💾 数据库状态异常，重新初始化...');
              await this.resetDatabase();
              await this.init();
            }
            
            // 延迟重试
            setTimeout(() => {
              this.performSaveOperation(data, attempt + 1).then(resolve).catch(reject);
            }, 2000 * attempt);
          } else {
            reject(new Error(`Transaction failed after ${maxAttempts} attempts: ${transaction.error?.message || 'Unknown error'}`));
          }
        };
        
        transaction.onabort = async (event) => {
          cleanup();
          console.error(`💾 IndexedDB 事务被中止 (尝试 ${attempt}):`, event);
          
          if (attempt < maxAttempts) {
            console.log(`💾 准备第 ${attempt + 1} 次重试...`);
            setTimeout(() => {
              this.performSaveOperation(data, attempt + 1).then(resolve).catch(reject);
            }, 2000 * attempt);
          } else {
            reject(new Error(`Transaction aborted after ${maxAttempts} attempts: ${transaction.error?.message || 'Unknown error'}`));
          }
        };
        
        const store = transaction.objectStore(this.storeName);
        
        console.log('💾 执行 put 操作...');
        const request = store.put(data);

        // 添加进度监控（仅在第一次尝试时）
        let progressTimer: NodeJS.Timeout | null = null;
        if (attempt === 1) {
          progressTimer = setInterval(() => {
            console.log('💾 IndexedDB 保存进行中...');
          }, 10000); // 改为10秒间隔
        }
        
        const cleanupProgress = () => {
          if (progressTimer) {
            clearInterval(progressTimer);
            progressTimer = null;
          }
        };
        
        request.onsuccess = () => {
          cleanupProgress();
          console.log(`💾 TLE数据保存请求成功，共${data.total}颗卫星 (尝试 ${attempt})`);
          // 注意：这里不调用 resolve()，等待 transaction.oncomplete
        };
        
        request.onerror = () => {
          cleanupProgress();
          cleanup();
          console.error(`💾 TLE数据保存请求失败 (尝试 ${attempt}):`, {
            error: request.error,
            errorName: request.error?.name,
            errorMessage: request.error?.message,
            readyState: request.readyState
          });
          
          if (attempt < maxAttempts) {
            console.log(`💾 准备第 ${attempt + 1} 次重试...`);
            setTimeout(() => {
              this.performSaveOperation(data, attempt + 1).then(resolve).catch(reject);
            }, 2000 * attempt);
          } else {
            reject(request.error || new Error('Put operation failed'));
          }
        };
        
      } catch (error) {
        console.error(`💾 保存操作异常 (尝试 ${attempt}):`, error);
        
        if (attempt < maxAttempts) {
          console.log(`💾 准备第 ${attempt + 1} 次重试...`);
          setTimeout(() => {
            this.performSaveOperation(data, attempt + 1).then(resolve).catch(reject);
          }, 2000 * attempt);
        } else {
          reject(error);
        }
      }
    });
  }

  /**
   * 清理旧数据
   */
  private async clearOldData(): Promise<void> {
    console.log('💾 开始清理旧数据...');
    try {
      // 这里可以实现清理逻辑，比如删除过期的缓存数据
      // 暂时只删除当前的TLE数据
      await this.deleteTleData();
      console.log('💾 旧数据清理完成');
    } catch (error) {
      console.error('💾 清理旧数据失败:', error);
    }
  }

  /**
   * 读取TLE数据
   */
  async getTleData(): Promise<TleDataCache | null> {
    if (!this.db) {
      await this.init();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      
      const request = store.get('tle-cache');

      request.onsuccess = () => {
        const result = request.result as TleDataCache | undefined;
        if (result) {
          // 验证数据结构的完整性
          if (!result.data || !Array.isArray(result.data) || typeof result.total !== 'number') {
            console.warn('⚠️ IndexedDB中的TLE数据结构无效:', result);
            resolve(null);
            return;
          }
          console.log(`读取到TLE缓存数据，共${result.total}颗卫星，最后更新时间：${result.lastUpdated}`);
        } else {
          console.log('未找到TLE缓存数据');
        }
        resolve(result || null);
      };

      request.onerror = () => {
        console.error('读取TLE数据失败:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 删除TLE数据
   */
  async deleteTleData(): Promise<void> {
    if (!this.db) {
      await this.init();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      const request = store.delete('tle-cache');

      request.onsuccess = () => {
        console.log('TLE缓存数据已删除');
        resolve();
      };

      request.onerror = () => {
        console.error('删除TLE数据失败:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 检查数据是否过期
   */
  isDataExpired(data: TleDataCache): boolean {
    const now = new Date();
    const nextUpdate = new Date(data.nextUpdate);
    return now >= nextUpdate;
  }

  /**
   * 获取数据状态信息
   */
  getDataStatus(data: TleDataCache | null): {
    hasData: boolean;
    isExpired: boolean;
    lastUpdated: string | null;
    nextUpdate: string | null;
    satelliteCount: number;
    dataAge: string;
  } {
    if (!data) {
      return {
        hasData: false,
        isExpired: true,
        lastUpdated: null,
        nextUpdate: null,
        satelliteCount: 0,
        dataAge: '无数据'
      };
    }

    const now = new Date();
    const lastUpdated = new Date(data.lastUpdated);
    const ageMs = now.getTime() - lastUpdated.getTime();
    const ageHours = Math.floor(ageMs / (1000 * 60 * 60));
    const ageMinutes = Math.floor((ageMs % (1000 * 60 * 60)) / (1000 * 60));
    
    let dataAge: string;
    if (ageHours > 0) {
      dataAge = `${ageHours}小时${ageMinutes}分钟前`;
    } else {
      dataAge = `${ageMinutes}分钟前`;
    }

    return {
      hasData: true,
      isExpired: this.isDataExpired(data),
      lastUpdated: data.lastUpdated,
      nextUpdate: data.nextUpdate,
      satelliteCount: data.total,
      dataAge
    };
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
      console.log('IndexedDB连接已关闭');
    }
  }

  /**
   * 重置数据库（公共方法，供用户手动调用）
   */
  async resetDatabaseManually(): Promise<void> {
    console.log('💾 手动重置数据库...');
    await this.resetDatabase();
    console.log('💾 数据库重置完成，请重新尝试操作');
  }

  /**
   * 获取数据库诊断信息
   */
  async getDiagnosticInfo(): Promise<object> {
    const info: any = {
      dbName: this.dbName,
      version: this.version,
      storeName: this.storeName,
      isConnected: !!this.db,
      initAttempts: this.initAttempts
    };

    try {
      // 获取存储配额信息
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate();
        info.storage = {
          quota: estimate.quota ? `${(estimate.quota / 1024 / 1024 / 1024).toFixed(2)} GB` : '未知',
          usage: estimate.usage ? `${(estimate.usage / 1024 / 1024).toFixed(2)} MB` : '未知',
          available: estimate.quota && estimate.usage ? 
            `${((estimate.quota - estimate.usage) / 1024 / 1024 / 1024).toFixed(2)} GB` : '未知'
        };
      }

      // 获取数据库列表
      const databases = await indexedDB.databases();
      info.existingDatabases = databases.map(db => ({
        name: db.name,
        version: db.version
      }));

      // 检查当前数据库状态
      if (this.db) {
        info.currentDatabase = {
          name: this.db.name,
          version: this.db.version,
          objectStoreNames: Array.from(this.db.objectStoreNames)
        };
      }

    } catch (error) {
      info.diagnosticError = error instanceof Error ? error.message : String(error);
    }

    return info;
  }

  /**
   * 存储星座数据
   */
  async saveConstellationData(data: ConstellationDataCache): Promise<void> {
    if (!this.db) {
      await this.init();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      const request = store.put(data);

      request.onsuccess = () => {
        console.log(`星座数据保存成功，共${data.total}个星座`);
        resolve();
      };

      request.onerror = () => {
        console.error('星座数据保存失败:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 读取星座数据
   */
  async getConstellationData(): Promise<ConstellationDataCache | null> {
    if (!this.db) {
      await this.init();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      
      const request = store.get('constellation-cache');

      request.onsuccess = () => {
        const result = request.result as ConstellationDataCache | undefined;
        if (result) {
          console.log(`读取到星座缓存数据，共${result.total}个星座，最后更新时间：${result.lastUpdated}`);
        } else {
          console.log('未找到星座缓存数据');
        }
        resolve(result || null);
      };

      request.onerror = () => {
        console.error('读取星座数据失败:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 删除星座数据
   */
  async deleteConstellationData(): Promise<void> {
    if (!this.db) {
      await this.init();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      const request = store.delete('constellation-cache');

      request.onsuccess = () => {
        console.log('星座缓存数据已删除');
        resolve();
      };

      request.onerror = () => {
        console.error('删除星座数据失败:', request.error);
        reject(request.error);
      };
    });
  }
}

// 导出单例实例
export const indexedDBHelper = new IndexedDBHelper(); 