/**
 * 多层缓存管理器
 * 解决IndexedDB不稳定问题，提供多种存储方案的自动降级
 * 
 * 缓存层级（按优先级）：
 * 1. 内存缓存 - 最快，页面刷新丢失
 * 2. localStorage - 持久化，容量有限但稳定
 * 3. sessionStorage - 会话级，稳定
 * 4. IndexedDB - 大容量，但可能不稳定
 */

import { TleDataCache, TleDataItem, indexedDBHelper } from './indexedDBHelper';
import * as pako from 'pako';

/**
 * 存储层枚举
 */
export enum StorageLayer {
  MEMORY = 'memory',
  LOCAL_STORAGE = 'localStorage', 
  SESSION_STORAGE = 'sessionStorage',
  INDEXED_DB = 'indexedDB'
}

/**
 * 缓存状态接口
 */
export interface CacheStatus {
  layer: StorageLayer;
  available: boolean;
  lastError?: string;
  dataSize?: string;
}

/**
 * 多层缓存管理器
 */
export class MultilayerCacheManager {
  private memoryCache: Map<string, TleDataCache> = new Map();
  private readonly STORAGE_KEY = 'space-tle-cache';
  private readonly CHUNK_SIZE = 1024 * 1024; // 1MB chunks for localStorage
  
  /**
   * 保存TLE数据到所有可用的存储层
   */
  async saveTleData(data: TleDataCache): Promise<void> {
    console.log('🔄 开始多层缓存保存...');
    
    const results: { layer: StorageLayer; success: boolean; error?: string }[] = [];
    
    // 1. 保存到内存缓存（最优先，总是成功）
    try {
      this.memoryCache.set('tle-cache', data);
      console.log('✅ 内存缓存保存成功');
      results.push({ layer: StorageLayer.MEMORY, success: true });
    } catch (error) {
      console.error('❌ 内存缓存保存失败:', error);
      results.push({ layer: StorageLayer.MEMORY, success: false, error: String(error) });
    }

    // 2. 优先保存到IndexedDB（完整数据，无大小限制）
    try {
      await indexedDBHelper.saveTleData(data);
      console.log('✅ IndexedDB保存成功（完整数据）');
      results.push({ layer: StorageLayer.INDEXED_DB, success: true });
    } catch (error) {
      console.error('⚠️ IndexedDB保存失败:', error);
      results.push({ layer: StorageLayer.INDEXED_DB, success: false, error: String(error) });
    }

    // 3. 保存到localStorage（可能需要精简数据）
    try {
      await this.saveToLocalStorageWithCleanup(data);
      console.log('✅ localStorage保存成功');
      results.push({ layer: StorageLayer.LOCAL_STORAGE, success: true });
    } catch (error) {
      console.error('❌ localStorage保存失败:', error);
      results.push({ layer: StorageLayer.LOCAL_STORAGE, success: false, error: String(error) });
    }

    // 4. 保存到sessionStorage（精简数据备用）
    try {
      await this.saveToSessionStorage(data);
      console.log('✅ sessionStorage保存成功');
      results.push({ layer: StorageLayer.SESSION_STORAGE, success: true });
    } catch (error) {
      console.error('❌ sessionStorage保存失败:', error);
      results.push({ layer: StorageLayer.SESSION_STORAGE, success: false, error: String(error) });
    }
    
    // 汇总结果
    const successCount = results.filter(r => r.success).length;
    console.log(`🎯 多层缓存保存完成: ${successCount}/${results.length} 层成功`);
    
    // 只要有一个存储层成功，就认为保存成功
    // 内存缓存几乎总是成功的，所以这里不会抛出错误
    if (successCount === 0) {
      console.error('⚠️ 所有存储层都保存失败，但系统将继续运行');
      // 不抛出错误，让系统继续运行
    }
    
    // 输出详细的保存结果
    results.forEach(result => {
      if (result.success) {
        console.log(`✅ ${result.layer} 保存成功`);
      } else {
        console.warn(`❌ ${result.layer} 保存失败: ${result.error}`);
      }
    });
  }
  
  /**
   * 带清理机制的localStorage保存
   */
  private async saveToLocalStorageWithCleanup(data: TleDataCache): Promise<void> {
    try {
      await this.saveToLocalStorage(data);
    } catch (error) {
      // 如果是配额错误，尝试清理后重试
      if (error instanceof Error && (
        error.name === 'QuotaExceededError' || 
        error.message.includes('quota') ||
        error.message.includes('storage')
      )) {
        console.log('🧹 localStorage配额已满，开始清理...');
        
        try {
          // 清理旧的TLE缓存数据
          await this.clearLocalStorageCache();
          console.log('🧹 localStorage清理完成，重试保存...');
          
          // 重试保存原始数据
          await this.saveToLocalStorage(data);
          console.log('✅ localStorage清理后保存成功');
        } catch (retryError) {
          console.warn('❌ localStorage清理后仍然保存失败，尝试保存精简版本:', retryError);
          
          try {
            // 尝试保存精简版本（只保留前5000颗卫星）
            const lightData: TleDataCache = {
              ...data,
              data: data.data.slice(0, 5000),
              total: Math.min(data.total, 5000),
              apiResponse: {
                ...data.apiResponse,
                total: Math.min(data.apiResponse.total, 5000)
              }
            };
            
            await this.saveToLocalStorage(lightData);
            console.log('✅ localStorage精简版本保存成功（前5000颗卫星）');
          } catch (lightError) {
            console.warn('❌ 精简版本仍然保存失败，尝试超精简版本:', lightError);
            
            try {
              // 尝试保存超精简版本（只保留前1000颗卫星）
              const miniData: TleDataCache = {
                ...data,
                data: data.data.slice(0, 1000),
                total: Math.min(data.total, 1000),
                apiResponse: {
                  ...data.apiResponse,
                  total: Math.min(data.apiResponse.total, 1000)
                }
              };
              
              await this.saveToLocalStorage(miniData);
              console.log('✅ localStorage超精简版本保存成功（前1000颗卫星）');
            } catch (miniError) {
              console.error('❌ 所有localStorage保存尝试都失败:', miniError);
              throw miniError;
            }
          }
        }
      } else {
        throw error;
      }
    }
  }
  
  /**
   * 清理localStorage中的TLE缓存数据
   */
  private async clearLocalStorageCache(): Promise<void> {
    try {
      console.log('🧹 开始清理localStorage缓存...');
      
      // 1. 清理TLE相关的所有数据
      const keysToRemove: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (
          key.startsWith(this.STORAGE_KEY) ||
          key.includes('tle') ||
          key.includes('satellite') ||
          key.includes('space')
        )) {
          keysToRemove.push(key);
        }
      }
      
      // 2. 清理所有相关数据
      keysToRemove.forEach(key => {
        try {
          localStorage.removeItem(key);
        } catch (error) {
          console.warn(`清理localStorage项目 ${key} 失败:`, error);
        }
      });
      
      console.log(`🧹 已清理 ${keysToRemove.length} 个localStorage项目`);
      
      // 3. 如果还是空间不足，进行更激进的清理
      try {
        // 测试是否还有空间
        const testKey = 'space-test-storage';
        const testData = 'x'.repeat(1024 * 100); // 100KB测试数据
        localStorage.setItem(testKey, testData);
        localStorage.removeItem(testKey);
        console.log('✅ localStorage空间清理成功');
      } catch (testError) {
        console.warn('⚠️ localStorage空间仍然不足，进行更彻底的清理...');
        
        // 清理所有非关键数据
        const allKeys: string[] = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && !this.isEssentialKey(key)) {
            allKeys.push(key);
          }
        }
        
        // 清理非关键数据
        allKeys.forEach(key => {
          try {
            localStorage.removeItem(key);
          } catch (error) {
            console.warn(`清理非关键数据 ${key} 失败:`, error);
          }
        });
        
        console.log(`🧹 激进清理：已清理 ${allKeys.length} 个非关键localStorage项目`);
      }
      
    } catch (error) {
      console.error('清理localStorage时出错:', error);
      throw error;
    }
  }
  
  /**
   * 判断是否为关键的localStorage键
   */
  private isEssentialKey(key: string): boolean {
    const essentialPrefixes = [
      'auth',
      'token',
      'user',
      'login',
      'session',
      'config',
      'settings'
    ];
    
    return essentialPrefixes.some(prefix => 
      key.toLowerCase().includes(prefix.toLowerCase())
    );
  }
  
  /**
   * 保存到localStorage（压缩分块）
   */
  private async saveToLocalStorage(data: TleDataCache): Promise<void> {
    const jsonString = JSON.stringify(data);
    let finalData = jsonString;
    
    // 尝试压缩
    try {
      const compressed = pako.gzip(new TextEncoder().encode(jsonString));
      const base64Compressed = btoa(String.fromCharCode(...compressed));
      finalData = base64Compressed;
      console.log(`📦 数据压缩: ${jsonString.length} -> ${finalData.length} 字节 (${((1 - finalData.length / jsonString.length) * 100).toFixed(1)}% 压缩率)`);
    } catch (error) {
      console.warn('⚠️ 压缩失败，使用原始数据:', error);
    }
    
    // 检查是否需要分块
    if (finalData.length > this.CHUNK_SIZE) {
      await this.saveChunkedToLocalStorage(finalData, finalData !== jsonString ? 'compressed' : 'raw');
    } else {
      localStorage.setItem(this.STORAGE_KEY, finalData);
      localStorage.setItem(`${this.STORAGE_KEY}_type`, finalData !== jsonString ? 'compressed' : 'raw');
    }
  }
  
  /**
   * 分块保存到localStorage
   */
  private async saveChunkedToLocalStorage(data: string, type: 'compressed' | 'raw'): Promise<void> {
    const chunks = [];
    for (let i = 0; i < data.length; i += this.CHUNK_SIZE) {
      chunks.push(data.substring(i, i + this.CHUNK_SIZE));
    }
    
    // 保存元数据
    localStorage.setItem(`${this.STORAGE_KEY}_meta`, JSON.stringify({
      type,
      chunkCount: chunks.length,
      totalSize: data.length
    }));
    
    // 保存分块
    for (let i = 0; i < chunks.length; i++) {
      localStorage.setItem(`${this.STORAGE_KEY}_chunk_${i}`, chunks[i]);
    }
    
    console.log(`📦 分块保存完成: ${chunks.length} 块`);
  }
  
  /**
   * 从所有存储层读取TLE数据（按优先级）
   */
  async getTleData(): Promise<TleDataCache | null> {
    console.log('🔍 开始多层缓存读取...');

    // 1. 优先从内存缓存读取
    const memoryData = this.memoryCache.get('tle-cache');
    if (memoryData) {
      console.log('✅ 从内存缓存读取成功');
      return memoryData;
    }

    // 2. 优先从IndexedDB读取（完整数据）
    try {
      const indexedDBData = await indexedDBHelper.getTleData();
      if (indexedDBData) {
        console.log('✅ 从IndexedDB读取成功（完整数据）');
        // 同时放入内存缓存
        this.memoryCache.set('tle-cache', indexedDBData);
        return indexedDBData;
      }
    } catch (error) {
      console.error('❌ IndexedDB读取失败:', error);
    }

    // 3. 从localStorage读取（可能是精简数据）
    try {
      const localStorageData = await this.loadFromLocalStorage();
      if (localStorageData) {
        console.log('✅ 从localStorage读取成功（可能是精简数据）');
        // 同时放入内存缓存
        this.memoryCache.set('tle-cache', localStorageData);
        return localStorageData;
      }
    } catch (error) {
      console.error('❌ localStorage读取失败:', error);
    }

    // 4. 最后从sessionStorage读取（精简数据）
    try {
      const sessionData = await this.loadFromSessionStorage();
      if (sessionData) {
        console.log('✅ 从sessionStorage读取成功（精简数据）');
        // 同时放入内存缓存
        this.memoryCache.set('tle-cache', sessionData);
        return sessionData;
      }
    } catch (error) {
      console.error('❌ sessionStorage读取失败:', error);
    }

    console.log('⚠️ 所有存储层都没有找到数据');
    return null;
  }
  
  /**
   * 从localStorage读取
   */
  private async loadFromLocalStorage(): Promise<TleDataCache | null> {
    try {
      // 检查是否是分块存储
      const metaStr = localStorage.getItem(`${this.STORAGE_KEY}_meta`);
      if (metaStr) {
        return await this.loadChunkedFromLocalStorage();
      }
      
      // 单块存储
      const data = localStorage.getItem(this.STORAGE_KEY);
      const type = localStorage.getItem(`${this.STORAGE_KEY}_type`) || 'raw';
      
      if (!data) return null;
      
      let jsonString = data;
      
      // 解压缩
      if (type === 'compressed') {
        try {
          const compressed = new Uint8Array(atob(data).split('').map(c => c.charCodeAt(0)));
          const decompressed = pako.ungzip(compressed);
          jsonString = new TextDecoder().decode(decompressed);
        } catch (error) {
          console.error('解压缩失败:', error);
          return null;
        }
      }
      
      const parsedData = JSON.parse(jsonString);

      // 验证解析后的数据结构
      if (!parsedData || typeof parsedData !== 'object' || !parsedData.data || !Array.isArray(parsedData.data)) {
        console.warn('⚠️ localStorage中的数据格式无效:', parsedData);
        return null;
      }

      return parsedData;
    } catch (error) {
      console.error('localStorage读取失败:', error);
      // 如果解析失败，清理损坏的数据
      try {
        localStorage.removeItem(this.STORAGE_KEY);
        localStorage.removeItem(`${this.STORAGE_KEY}_type`);
        console.log('🧹 已清理损坏的localStorage数据');
      } catch (cleanupError) {
        console.error('清理localStorage失败:', cleanupError);
      }
      return null;
    }
  }
  
  /**
   * 分块读取localStorage
   */
  private async loadChunkedFromLocalStorage(): Promise<TleDataCache | null> {
    try {
      const metaStr = localStorage.getItem(`${this.STORAGE_KEY}_meta`);
      if (!metaStr) return null;
      
      const meta = JSON.parse(metaStr);
      const chunks: string[] = [];
      
      // 读取所有分块
      for (let i = 0; i < meta.chunkCount; i++) {
        const chunk = localStorage.getItem(`${this.STORAGE_KEY}_chunk_${i}`);
        if (!chunk) {
          console.error(`分块 ${i} 丢失`);
          return null;
        }
        chunks.push(chunk);
      }
      
      let data = chunks.join('');
      
      // 解压缩
      if (meta.type === 'compressed') {
        try {
          const compressed = new Uint8Array(atob(data).split('').map(c => c.charCodeAt(0)));
          const decompressed = pako.ungzip(compressed);
          data = new TextDecoder().decode(decompressed);
        } catch (error) {
          console.error('解压缩失败:', error);
          return null;
        }
      }
      
      const parsedData = JSON.parse(data);

      // 验证解析后的数据结构
      if (!parsedData || typeof parsedData !== 'object' || !parsedData.data || !Array.isArray(parsedData.data)) {
        console.warn('⚠️ localStorage分块数据格式无效:', parsedData);
        return null;
      }

      return parsedData;
    } catch (error) {
      console.error('分块读取失败:', error);
      // 如果解析失败，清理损坏的分块数据
      try {
        const metaStr = localStorage.getItem(`${this.STORAGE_KEY}_meta`);
        if (metaStr) {
          const meta = JSON.parse(metaStr);
          for (let i = 0; i < meta.chunkCount; i++) {
            localStorage.removeItem(`${this.STORAGE_KEY}_chunk_${i}`);
          }
          localStorage.removeItem(`${this.STORAGE_KEY}_meta`);
          console.log('🧹 已清理损坏的localStorage分块数据');
        }
      } catch (cleanupError) {
        console.error('清理localStorage分块数据失败:', cleanupError);
      }
      return null;
    }
  }
  
  /**
   * 保存到sessionStorage
   */
  private async saveToSessionStorage(data: TleDataCache): Promise<void> {
    try {
      // 只保存精简版本到sessionStorage
      const lightData = {
        ...data,
        data: data.data.slice(0, 1000) // 只保存前1000个卫星
      };
      sessionStorage.setItem(this.STORAGE_KEY, JSON.stringify(lightData));
    } catch (error) {
      if (error instanceof Error && error.name === 'QuotaExceededError') {
        // 如果空间不足，保存更精简的版本
        const miniData = {
          ...data,
          data: data.data.slice(0, 100) // 只保存前100个卫星
        };
        sessionStorage.setItem(this.STORAGE_KEY, JSON.stringify(miniData));
      } else {
        throw error;
      }
    }
  }
  
  /**
   * 从sessionStorage读取
   */
  private async loadFromSessionStorage(): Promise<TleDataCache | null> {
    try {
      const data = sessionStorage.getItem(this.STORAGE_KEY);
      if (!data) return null;

      const parsedData = JSON.parse(data);

      // 验证解析后的数据结构
      if (!parsedData || typeof parsedData !== 'object' || !parsedData.data || !Array.isArray(parsedData.data)) {
        console.warn('⚠️ sessionStorage中的数据格式无效:', parsedData);
        return null;
      }

      return parsedData;
    } catch (error) {
      console.error('sessionStorage读取失败:', error);
      // 如果解析失败，清理损坏的数据
      try {
        sessionStorage.removeItem(this.STORAGE_KEY);
        console.log('🧹 已清理损坏的sessionStorage数据');
      } catch (cleanupError) {
        console.error('清理sessionStorage失败:', cleanupError);
      }
      return null;
    }
  }
  
  /**
   * 手动清理localStorage缓存（公共方法）
   * 用于解决localStorage配额问题
   */
  async clearLocalStorageOnly(): Promise<void> {
    console.log('🧹 手动清理localStorage缓存...');
    try {
      await this.clearLocalStorageCache();
      console.log('✅ localStorage缓存清理完成');
    } catch (error) {
      console.error('❌ localStorage缓存清理失败:', error);
      throw error;
    }
  }

  /**
   * 强制清理所有localStorage数据（紧急情况使用）
   */
  async emergencyClearLocalStorage(): Promise<void> {
    console.log('🚨 紧急清理所有localStorage数据...');
    try {
      // 获取所有localStorage键
      const allKeys: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          allKeys.push(key);
        }
      }

      // 清理所有数据
      allKeys.forEach(key => {
        try {
          localStorage.removeItem(key);
        } catch (error) {
          console.warn(`清理localStorage项目 ${key} 失败:`, error);
        }
      });

      console.log(`🚨 紧急清理完成，已清理 ${allKeys.length} 个localStorage项目`);
    } catch (error) {
      console.error('❌ 紧急清理localStorage失败:', error);
      throw error;
    }
  }
  
  /**
   * 清理所有缓存
   */
  async clearAllCache(): Promise<void> {
    console.log('🗑️ 清理所有缓存...');
    
    // 清理内存缓存
    this.memoryCache.clear();
    
    // 清理localStorage
    try {
      const metaStr = localStorage.getItem(`${this.STORAGE_KEY}_meta`);
      if (metaStr) {
        const meta = JSON.parse(metaStr);
        for (let i = 0; i < meta.chunkCount; i++) {
          localStorage.removeItem(`${this.STORAGE_KEY}_chunk_${i}`);
        }
        localStorage.removeItem(`${this.STORAGE_KEY}_meta`);
      }
      localStorage.removeItem(this.STORAGE_KEY);
      localStorage.removeItem(`${this.STORAGE_KEY}_type`);
    } catch (error) {
      console.error('清理localStorage失败:', error);
    }
    
    // 清理sessionStorage
    try {
      sessionStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.error('清理sessionStorage失败:', error);
    }
    
    // 清理IndexedDB
    try {
      await indexedDBHelper.deleteTleData();
    } catch (error) {
      console.error('清理IndexedDB失败:', error);
    }
    
    console.log('✅ 所有缓存清理完成');
  }
  
  /**
   * 获取各存储层状态
   */
  async getStorageStatus(): Promise<CacheStatus[]> {
    const status: CacheStatus[] = [];
    
    // 内存缓存状态
    const memoryData = this.memoryCache.get('tle-cache');
    status.push({
      layer: StorageLayer.MEMORY,
      available: true,
      dataSize: memoryData ? `${JSON.stringify(memoryData).length} bytes` : '无数据'
    });
    
    // localStorage状态
    try {
      const localData = localStorage.getItem(this.STORAGE_KEY);
      status.push({
        layer: StorageLayer.LOCAL_STORAGE,
        available: true,
        dataSize: localData ? `${localData.length} bytes` : '无数据'
      });
    } catch (error) {
      status.push({
        layer: StorageLayer.LOCAL_STORAGE,
        available: false,
        lastError: String(error)
      });
    }
    
    // sessionStorage状态
    try {
      const sessionData = sessionStorage.getItem(this.STORAGE_KEY);
      status.push({
        layer: StorageLayer.SESSION_STORAGE,
        available: true,
        dataSize: sessionData ? `${sessionData.length} bytes` : '无数据'
      });
    } catch (error) {
      status.push({
        layer: StorageLayer.SESSION_STORAGE,
        available: false,
        lastError: String(error)
      });
    }
    
    // IndexedDB状态
    try {
      const indexedDBData = await indexedDBHelper.getTleData();
      status.push({
        layer: StorageLayer.INDEXED_DB,
        available: true,
        dataSize: indexedDBData ? `${JSON.stringify(indexedDBData).length} bytes` : '无数据'
      });
    } catch (error) {
      status.push({
        layer: StorageLayer.INDEXED_DB,
        available: false,
        lastError: String(error)
      });
    }
    
    return status;
  }
}

// 导出单例实例
export const multilayerCacheManager = new MultilayerCacheManager();

// 全局暴露，方便调试
if (typeof window !== 'undefined') {
  (window as any).multilayerCacheManager = multilayerCacheManager;
} 