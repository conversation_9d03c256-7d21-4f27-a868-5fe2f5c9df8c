import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { CaretRightOutlined, ReloadOutlined, InfoCircleOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Tree, Switch, Select, Collapse, Button, message, Spin, Tooltip, Badge } from 'antd';
import type { DataNode, EventDataNode } from 'antd/es/tree';
import type { DefaultOptionType } from 'antd/es/select';
import type { Key } from 'react';
import { Check } from 'lucide-react';
import { CesiumController } from '../controllers/CesiumController';
import { apiService } from '../../../services/apiService';
import { API_BASE_URL } from '../../../services/api';
import { formatSatelliteName, extractNoradId, extractSatelliteName } from '../../../utils/satellite';
import { tleDataManager, TleDataStatus } from '../../../services/tleDataManager';
import { TleDataItem } from '../../../utils/indexedDBHelper';

const PanelContainer = styled.div<{ $visible: boolean }>`
  position: absolute;
  left: 80px;
  top: 50%;
  transform: translateY(-50%);
  width: 220px;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 12px;
  color: white;
  opacity: ${props => props.$visible ? 1 : 0};
  visibility: ${props => props.$visible ? 'visible' : 'hidden'};
  transition: all 0.3s ease;
  z-index: 90;
  max-height: 80vh;
  overflow-y: auto;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
`;

const StyledTree = styled(Tree)`
  background: transparent;
  color: rgba(255, 255, 255, 0.85);

  // 为所有树节点内容添加悬停效果
  .ant-tree-treenode {
    &:hover {
      > .ant-tree-node-content-wrapper {
        color: #1890ff !important;
      }
    }
  }

  .ant-tree-node-content-wrapper {
    color: rgba(255, 255, 255, 0.85);
    transition: all 0.3s ease;
    
    &:hover {
      background: transparent !important;
      color: #1890ff !important; // 鼠标悬停时使用蓝色
    }
    
    &.ant-tree-node-selected {
      background: transparent !important;
      color: #1890ff !important; // 选中时也使用蓝色
    }
  }

  .ant-tree-switcher {
    color: rgba(255, 255, 255, 0.45);

    &:hover {
      color: rgba(255, 255, 255, 0.85);
    }
  }

  .ant-tree-indent-unit {
    width: 16px;
  }

  .ant-tree-treenode {
    padding: 3px 0;
  }
`;

const StyledSelect = styled(Select)`
  width: 120px;
  margin-left: 8px;
  position: relative;
  z-index: 100;

  // 主选择框样式
  .ant-select-selector {
    background: rgba(0, 0, 0, 0.3) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 4px !important;
  }

  // 搜索框样式
  .ant-select-selection-search-input {
    color: white !important;
  }

  // 选中项的样式
  .ant-select-selection-item {
    color: white !important;
    padding-right: 20px !important; // 为删除图标留出空间

    // 删除图标样式
    .ant-select-selection-item-remove {
      color: rgba(255, 255, 255, 0.45) !important;
      font-size: 12px;
      right: 4px !important;
      opacity: 0.8;
      transition: all 0.3s;

      &:hover {
        color: white !important;
        opacity: 1;
      }
    }
  }

  // 多选标签样式
  .ant-select-selection-overflow-item {
    .ant-select-selection-item {
      background: transparent !important;
      border: none !important;
      padding: 0 20px 0 4px !important; // 调整内边距
      margin: 2px 4px 2px 0 !important;
    }
  }

  // 占位符样式
  .ant-select-selection-placeholder {
    color: rgba(255, 255, 255, 0.65) !important;
  }
`;

// 新增：全局样式覆盖
const GlobalSelectDropdown = styled.div`
  // 下拉菜单容器样式
  .ant-select-dropdown {
    background: rgba(0, 0, 0, 0.3) !important;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    
    // 下拉选项样式
    .ant-select-item {
      color: white !important;
      padding: 8px 12px;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1) !important;
      }
      
      &.ant-select-item-option-selected {
        background: transparent !important;
        
        &::after {
          content: "✓";
          position: absolute;
          right: 12px;
          color: white;
        }
      }
    }
  }
`;

const TreeNodeContent = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

const SelectContainer = styled.div<{ $visible: boolean }>`
  margin-top: 8px;
  margin-left: 18px;
  height: ${props => props.$visible ? 'auto' : '0'};
  opacity: ${props => props.$visible ? 1 : 0};
  overflow: hidden;
  transition: all 0.3s ease;
`;

// 改进卫星下拉选择区域的样式
const SatelliteSelectContainer = styled.div`
  margin-top: 12px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const SatelliteSelectLabel = styled.div`
  margin-bottom: 6px;
  color: rgba(255, 255, 255, 0.85);
  font-size: 13px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const SelectAllButton = styled.button`
  background: transparent;
  border: none;
  color: #1890ff;
  font-size: 12px;
  cursor: pointer;
  padding: 0;
  margin-left: 8px;
  
  &:hover {
    text-decoration: underline;
  }
`;

// 添加确认按钮样式
const ConfirmButton = styled.button`
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 16px;
  font-size: 13px;
  cursor: pointer;
  margin-top: 12px;
  width: 100%;
  transition: all 0.3s ease;
  
  &:hover {
    background: #40a9ff;
  }
  
  &:active {
    background: #096dd9;
  }
  
  &:disabled {
    background: #d9d9d9;
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
  }
`;

// 添加加载进度条样式
const LoadingProgress = styled.div`
  margin: 20px auto;
  width: 80%;
  text-align: center;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 6px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  margin: 8px 0;
  overflow: hidden;
`;

const ProgressFill = styled.div<{ $progress: number }>`
  height: 100%;
  width: ${props => props.$progress}%;
  background-color: #1890ff;
  border-radius: 3px;
  transition: width 0.3s ease;
`;

const LoadingText = styled.div`
  color: rgba(255, 255, 255, 0.85);
  font-size: 13px;
  margin-top: 4px;
`;

interface LayersPanelProps {
  visible: boolean;
  cesiumController?: CesiumController;
}

// 定义API响应的接口，与实际返回值匹配
interface ConstellationWithTleResponse {
  total: number;
  constellations: Array<{
    name: string;
    satelliteCount: number;
  }>;
}

// 定义卫星数据接口
interface SatelliteItem {
  id: number;
  satellite_name: Array<{value: string, sources: string[]}>;
  alternative_name: Array<{value: string, sources: string[]}>;
  norad_id: Array<{value: number, sources: string[]}>;
  cospar_id: Array<{value: string, sources: string[]}>;
  constellation: Array<{value: string, sources: string[]}>;
}

// 定义卫星列表响应接口
interface SatelliteListResponse {
  success: boolean;
  total: number;
  page: number;
  limit: number;
  results: SatelliteItem[];
}

// 添加TLE数据接口
interface SatelliteTleResponse {
  success: boolean;
  total: number;
  results: Array<{
    satellite_num: number;
    tle_raw: string;
    satellite_name: string;
    norad_id: number;
    norad_type: string;
    cospar_id: string;
    epoch: string;
    orbital_elements: {
      inc_deg: number;
      raan_deg: number;
      ecc: number;
      arg_peri_deg: number;
      mean_anom_deg: number;
      day_laps: number;
      orbit_num: number;
      orbital_period_min: number;
      sema_km: number;
      arg_alt_km: number;
      arg_apo_deg: number;
      launch_time: string;
    };
    // 其他数据省略
  }>;
}

// 更新API响应类型
interface ApiResponse<T> {
  success: boolean;
  error?: string;
  results: T;
  [key: string]: any;
}

// 添加位置数据的接口定义
interface SatellitePosition {
  longitude: number;
  latitude: number;
  altitude: number;
  velocity?: {
    x: number;
    y: number;
    z: number;
  };
}

interface SatellitePositionData {
  satId: string;
  name: string;
    line1: string;
    line2: string;
  constellation?: string; // 添加星座信息字段
}

// 更新选项类型定义
interface SatelliteOption {
  label: string;
  value: string;
  id: number;
  norad_id?: number;
  constellation: string; // 添加星座信息字段
  searchText?: string; // 用于搜索的文本
}

export function LayersPanel({ visible, cesiumController }: LayersPanelProps) {
  const [expandedKeys, setExpandedKeys] = useState<string[]>([
    'space', 'ground', 'satellites'
  ]);
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [selectedOptions, setSelectedOptions] = useState<Record<string, string[]>>({
    'constellation': [],
    'launch-site': [],
  });
  const [constellationOptions, setConstellationOptions] = useState<{ label: string; value: string }[]>([]);
  const [loading, setLoading] = useState(false);
  
  // 新增状态来跟踪选中的星座和该星座的卫星
  const [selectedConstellation, setSelectedConstellation] = useState<string | null>(null);
  const [satelliteOptions, setSatelliteOptions] = useState<SatelliteOption[]>([]);
  const [selectedSatellites, setSelectedSatellites] = useState<string[]>([]);
  const [loadingSatellites, setLoadingSatellites] = useState(false);
  const [showSatelliteSelect, setShowSatelliteSelect] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  // 新增状态控制TLE数据加载
  const [loadingTleData, setLoadingTleData] = useState(false);
  const [tleLoadingProgress, setTleLoadingProgress] = useState<number>(0);

  // 新增缓存变量
  const [cachedSatelliteIds, setCachedSatelliteIds] = useState<Record<string, number[]>>({});

  // 添加时间范围状态
  const [currentTimeRange, setCurrentTimeRange] = useState<{start: Date, end: Date} | null>(null);

  // 轨道显示状态
  const [orbitDisplayEnabled, setOrbitDisplayEnabled] = useState(true);

  // 添加卫星标签显示状态
  const [satelliteLabelsEnabled, setSatelliteLabelsEnabled] = useState(true);

  // 在state声明区域添加卫星视锥状态管理
  const [satelliteSensorsEnabled, setSatelliteSensorsEnabled] = useState<boolean>(false);

  // 初始化TLE数据管理器
  useEffect(() => {
    const initTleManager = async () => {
      try {
        // 设置事件监听器
        tleDataManager.setListeners({
          onUpdateStart: () => {
            console.log('TLE数据开始更新');
          },
          onUpdateProgress: (progress, message) => {
            console.log(`TLE数据更新进度: ${progress}% - ${message}`);
          },
          onUpdateComplete: (data) => {
            console.log('TLE数据更新完成:', data.total);
            message.success({ content: `TLE数据更新完成，共 ${data.total} 颗卫星`, key: 'refresh-tle', duration: 3 });
          },
          onUpdateError: (error) => {
            console.error('TLE数据更新失败:', error);
            message.error({ content: `TLE数据更新失败: ${error}`, key: 'refresh-tle', duration: 3 });
          },
          onStatusChange: (status) => {
            setTleDataStatus(status);
          }
        });

        // 初始化管理器
        await tleDataManager.init();
      } catch (error) {
        console.error('初始化TLE数据管理器失败:', error);
        message.error('初始化TLE数据管理器失败');
      }
    };

    initTleManager();

    // 清理函数
    return () => {
      tleDataManager.destroy();
    };
  }, []);

  // 增加这些状态变量
  const [selectedTypeKey, setSelectedTypeKey] = useState<string>('satellites');
  const [tleLoading, setTleLoading] = useState<boolean>(false);
  const [selectedSatelliteKeys, setSelectedSatelliteKeys] = useState<string[]>([]);

  // 新增状态来跟踪所有卫星加载状态
  const [loadingAllSatellites, setLoadingAllSatellites] = useState(false);
  const [allSatellitesProgress, setAllSatellitesProgress] = useState(0);

  // TLE数据管理状态
  const [tleDataStatus, setTleDataStatus] = useState<TleDataStatus>({
    hasData: false,
    isExpired: true,
    lastUpdated: null,
    nextUpdate: null,
    satelliteCount: 0,
    dataAge: '无数据',
    isUpdating: false,
    lastError: null
  });

  // 获取卫星列表
  const fetchSatellitesByConstellation = async (constellationName: string) => {
    if (!constellationName) return [];
    
    try {
      setLoadingSatellites(true);
      
      let allSatellites: SatelliteItem[] = [];
      let currentPage = 1;
      let totalItems = 0;
      let hasMorePages = true;
      
      // 循环获取所有页的数据
      while (hasMorePages) {
        const response = await apiService.post<SatelliteListResponse>(
          '/api/v1/database/filter-satellites',
          {
            constellationName,
            hasTleData: true, // 添加参数，只获取有TLE数据的卫星
            page: currentPage,
            limit: 100
          }
        );
        
        if (response.success && response.results.length > 0) {
          allSatellites = [...allSatellites, ...response.results];
          totalItems = response.total;
          
          // 更新加载进度
          const progress = Math.floor((allSatellites.length / totalItems) * 100);
          setLoadingProgress(progress);
          
          // 检查是否还有更多页
          if (allSatellites.length >= totalItems || response.results.length < 100) {
            hasMorePages = false;
          } else {
            currentPage++;
          }
        } else {
          hasMorePages = false;
        }
      }
      
      // 转换为选择器组件需要的格式
      // 修改名称格式：同时在标签和值中使用"名称-ID"格式
      const options = allSatellites.map(sat => {
        // 确保正确获取卫星名称，避免使用Unknown
        const name = sat.satellite_name?.[0]?.value || 
                    sat.alternative_name?.[0]?.value || 
                    `Satellite-${sat.norad_id?.[0]?.value}`;
        const noradId = sat.norad_id?.[0]?.value || 0;
        const constellation = sat.constellation?.[0]?.value || constellationName;
        
        // 组合名称和NORAD ID作为值和标签
        const formattedName = formatSatelliteName(name, noradId);
        
        console.log(`创建卫星选项: 原始名称=${name}, NORAD ID=${noradId}, 格式化名称=${formattedName}`);
        
        return {
          label: formattedName, // 显示格式化的名称（名称-ID）
          value: formattedName, // 内部值使用相同的格式化名称
          searchText: `${name} ${noradId}`, // 用于搜索的文本
          id: sat.id,
          norad_id: noradId,
          constellation
        };
      });
      
      // 设置卫星选择选项和缓存卫星ID
      setSatelliteOptions(options);
      
      // 更新缓存的卫星ID，方便后续获取TLE数据
      const satelliteIdMap: Record<string, number[]> = {};
      options.forEach(option => {
        if (satelliteIdMap[option.constellation]) {
          satelliteIdMap[option.constellation].push(option.norad_id);
        } else {
          satelliteIdMap[option.constellation] = [option.norad_id];
        }
      });
      setCachedSatelliteIds(satelliteIdMap);
      
      return options;
    } catch (error) {
      console.error('获取卫星列表失败:', error);
      message.error('获取卫星列表失败');
      return [];
    } finally {
      setLoadingSatellites(false);
    }
  };

  // 获取TLE数据
  const fetchTleData = async (noradIds: (number | null)[]): Promise<any[]> => {
    // 过滤掉null值
    const validNoradIds = noradIds.filter((id): id is number => id !== null);
    
    if (validNoradIds.length === 0) return [];
    
    try {
      console.log(`开始获取TLE数据: 卫星IDs = ${validNoradIds.join(', ')}`);
      
      // 将noradIds分批处理，每批最多100个
      const batchSize = 100;
      const batches: number[][] = [];
      
      for (let i = 0; i < validNoradIds.length; i += batchSize) {
        batches.push(validNoradIds.slice(i, i + batchSize));
      }
      
      let allTleData: any[] = [];
      let processedBatches = 0;
      
      // 处理每一批
      for (const batch of batches) {
        console.log(`请求批次 ${processedBatches + 1}/${batches.length}, IDs: ${batch.join(', ')}`);
        
        try {
          const response = await apiService.post<ApiResponse<any[]>>(
          '/orbit/bulk-tle',
            { 
              norad_ids: batch,
              hasTleData: true // 添加参数，确保返回的卫星有TLE数据
            }
        );
        
          console.log(`TLE API响应:`, response);
          
          if (response && response.success && response.results) {
            console.log(`成功获取TLE数据: ${response.results.length} 条记录`);
          allTleData = [...allTleData, ...response.results];
          } else {
            console.warn(`TLE API返回异常: success=${response?.success}, results长度=${response?.results?.length || 0}`);
          }
        } catch (apiError) {
          console.error(`批次 ${processedBatches + 1} TLE数据请求失败:`, apiError);
        }
        
        processedBatches++;
        // 更新TLE加载进度
        const progress = Math.round((processedBatches / batches.length) * 100);
        setTleLoadingProgress(progress);
      }
      
      console.log(`TLE数据获取完成, 共 ${allTleData.length} 条记录`);
      
      // 移除默认TLE数据创建逻辑，如果没有获取到数据，就直接返回空数组
      if (allTleData.length === 0) {
        console.warn(`未获取到TLE数据，不创建默认TLE数据，不显示卫星`);
        message.warning('未获取到有效的TLE数据，无法显示卫星');
      }
      
      return allTleData;
    } catch (error) {
      console.error('获取TLE数据失败:', error);
      return [];
    }
  };

  // 卫星选择变更处理函数
  const handleSatelliteSelectionChange = (values: string[]) => {
    // 仅更新选中的卫星列表，不立即显示卫星
    setSelectedSatellites(values);
    
    // 记录日志，但不执行显示操作
      if (values.length === 0) {
      console.log('已清除卫星选择');
      } else {
      console.log(`已选择 ${values.length} 颗卫星，等待用户点击确认按钮显示`);
    }
  };

  // 选择全部卫星
  const handleSelectAllSatellites = () => {
    const allSatelliteValues = satelliteOptions.map(option => option.value);
    setSelectedSatellites(allSatelliteValues);
    console.log(`已选择所有 ${allSatelliteValues.length} 颗卫星，等待用户点击确认按钮显示`);
  };

  // 取消选择所有卫星
  const handleUnselectAllSatellites = () => {
    setSelectedSatellites([]);
    console.log('已清除所有卫星选择');
  };

  // 获取星座数据
  useEffect(() => {
    const fetchConstellations = async () => {
      setLoading(true);
      try {
        console.log('使用apiService获取星座数据...');
        
        // 使用apiService获取星座数据，指定返回类型
        const response = await apiService.get<ConstellationWithTleResponse>('/local/constellation/with-tle');
        
        console.log('星座数据获取成功:', response);
        
        // 转换数据格式 - 修改：去掉数量显示
        const options = response.constellations.map(constellation => ({
          label: constellation.name,
          value: constellation.name
        }));
        
        console.log('处理后的星座选项数量:', options.length);
        
        if (options.length === 0) {
          throw new Error('API返回的星座列表为空');
        }
        
        setConstellationOptions(options);
      } catch (error) {
        console.error('获取星座数据失败:', error);
        
        // 设置一些默认选项以防API调用失败
        const defaultOptions = [
          { label: 'Starlink', value: 'Starlink' },
          { label: 'BeiDou', value: 'BeiDou' },
          { label: 'GPS', value: 'GPS' },
          { label: 'Galileo', value: 'Galileo' },
          { label: 'GaoFen', value: 'GaoFen' },
          { label: 'FengYun', value: 'FengYun' },
        ];
        console.log('使用默认星座选项:', defaultOptions);
        setConstellationOptions(defaultOptions);
      } finally {
        setLoading(false);
      }
    };

    fetchConstellations();
  }, []);
  
  // 修改星座选择处理函数，在选择星座时获取卫星列表
  const handleSelectChange = async (key: string, values: string[]) => {
    // 更新选择的值
    setSelectedOptions(prev => ({
      ...prev,
      [key]: values
    }));

    // 根据选择显示或隐藏卫星
    if (key === 'constellation') {
      // 移除之前的卫星
      const prevValues = selectedOptions[key] || [];
      prevValues.forEach(value => {
        cesiumController?.hideConstellation(value);
        // 如果卫星视场图层被选中，也需要隐藏对应的视场
        if (checkedKeys.includes('satellite-view')) {
          cesiumController?.hideSatelliteSensors(value);
        }
      });

      // 如果选择了星座，则获取该星座的卫星列表
      if (values.length > 0) {
        // 先设置星座
        setSelectedConstellation(values[0]);
        
        // 然后立即显示加载状态
        setLoadingSatellites(true);
        setSatelliteOptions([]);
        setLoadingProgress(0);
        console.log(`星座已选择: ${values[0]}, 开始获取卫星列表`);
        
        // 获取卫星列表
        try {
          // 这里使用await等待数据回来
          const options = await fetchSatellitesByConstellation(values[0]);
          console.log(`获取到卫星列表: ${options.length} 个卫星`);
        } catch (error) {
          console.error('获取卫星列表出错:', error);
          message.error('获取卫星列表失败');
        } finally {
          setLoadingSatellites(false); // 确保在任何情况下都重置加载状态
        }
      } else {
        // 如果没有选择星座，则清空相关状态
        console.log('取消选择星座');
        setSelectedConstellation(null);
        setSatelliteOptions([]);
        setSelectedSatellites([]);
      }
    }
  };

  // 修改: 选择确认处理函数
  const handleConfirmSelection = async () => {
    try {
      console.log("确认选择，类型:", selectedTypeKey);
      console.log("已选择的卫星:", selectedSatellites.length);
      
      // 使用已经选择的卫星列表而不是selectedSatelliteKeys
      if (selectedSatellites.length === 0) {
        message.info('请选择至少一颗卫星');
        return;
      }
      
      setTleLoading(true);
      setLoadingTleData(true);
      
      // 获取选中卫星的NORAD ID
      const noradIds = selectedSatellites.map(satelliteName => {
        const id = extractNoradId(satelliteName);
        return id ? parseInt(id, 10) : null;
      }).filter((id): id is number => id !== null);
      
      if (noradIds.length === 0) {
        message.warning('所选卫星没有有效的NORAD ID');
        setTleLoading(false);
        setLoadingTleData(false);
        return;
      }
      
      // 清空进度并显示开始处理消息
      setTleLoadingProgress(0);
      message.info(`开始处理 ${noradIds.length} 颗卫星的TLE数据`);
      console.log(`处理卫星NORAD IDs:`, noradIds);
      
      // 从satelliteOptions中获取完整的卫星选项信息
      const selectedOptions = satelliteOptions.filter(option => 
        selectedSatellites.includes(option.value)
      );
      
      // 分批处理卫星
      await processAndDisplaySatellitesByBatch(noradIds, selectedOptions);
      setTleLoading(false);
      setLoadingTleData(false);
      
      // 处理其他选择类型 - 这里保留其他逻辑...
    } catch (error) {
      console.error('处理选择时出错:', error);
      message.error('处理选择时出错: ' + (error instanceof Error ? error.message : String(error)));
      setTleLoading(false);
      setLoadingTleData(false);
    }
  };

  // 新增: 分批处理和显示卫星
  const processAndDisplaySatellitesByBatch = async (noradIds: number[], satelliteOptions: SatelliteOption[]) => {
    if (!noradIds.length || !cesiumController) return;
    
    // 将noradIds分批处理，每批最多100个
    const batchSize = 50; // 减小批次大小，提高成功率
    const batches: number[][] = [];
    
    for (let i = 0; i < noradIds.length; i += batchSize) {
      batches.push(noradIds.slice(i, i + batchSize));
    }
    
    let processedBatches = 0;
    let totalProcessedSatellites = 0;
    
    // 静默分批处理，减少日志输出
    
    // 创建一个全局唯一的消息key
    const messageKey = 'satellite-loading-progress';
    
    // 初始化进度条，显示0%
    message.loading({ 
      content: `处理卫星数据 (0/${noradIds.length}) 0%`, 
      key: messageKey, 
      duration: 0 
    });
    
    // 处理每一批
          for (const batch of batches) {
        try {
                  // 获取这一批次的TLE数据
          const tleDataBatch = await fetchTleData(batch);
        
        if (tleDataBatch.length > 0) {
          // 根据卫星选项创建参数映射
          const satelliteOptionsMap = satelliteOptions.reduce<Record<string, SatelliteOption>>((map, option) => {
            const noradId = extractNoradId(option.value);
            if (noradId) map[noradId] = option;
            return map;
          }, {});
          
          // 准备卫星数据 - 只提取TLE数据，添加数据验证
          const satelliteData = tleDataBatch
            .filter(data => isValidTleData(data))
            .map(data => {
              // 正确解析TLE数据行
              let line1 = '', line2 = '';
              
              // 将TLE数据按行分割
              const tleParts = (data.tle_raw || '').split('\n');
              
              // 遍历所有行，查找标准TLE行
              for (const line of tleParts) {
                const trimmedLine = line.trim();
                if (trimmedLine.startsWith('1 ')) {
                  line1 = trimmedLine;
                } else if (trimmedLine.startsWith('2 ')) {
                  line2 = trimmedLine;
                }
              }
              
              // 确保找到了两行标准TLE数据
              if (!line1 || !line2) {
                console.warn(`无法解析标准TLE行: ${data.satellite_name}`);
                return null;
              }
              
              // 获取对应的卫星选项数据
              // 添加数据验证，确保norad_id存在且有效
              if (!data.norad_id || typeof data.norad_id !== 'number') {
                console.warn('⚠️ 无效的norad_id:', data);
                return null;
              }
              const noradId = data.norad_id.toString();
              const satOption = satelliteOptionsMap[noradId];
              
              // 使用原始卫星名称
              const satName = data.satellite_name || (satOption ? satOption.label : `Satellite-${noradId}`);
              
              // 确保星座信息非空
              const constellation = satOption?.constellation || 'default';
              
              // 构建卫星数据对象
              return {
                id: noradId,
                name: satName,
                line1,
                line2,
                constellation
              };
            })
            .filter(Boolean);
          
          if (satelliteData.length > 0) {
            // 构建需要的数据结构
            const satellitesData = {
              tleData: satelliteData,
              isFirstBatch: processedBatches === 0, // 只有第一批清除已有卫星
              timeRange: {
                // 设置60分钟的轨道计算范围
                start: new Date(),
                end: new Date(new Date().getTime() + 60 * 60 * 1000)
              },
              keepCameraPosition: true // 添加新参数，保持相机位置不变
            };
            
            // 调用显示方法 - 通过类型断言避免类型错误
            (cesiumController as any).displaySatellitesWithTle(satellitesData);
            
            totalProcessedSatellites += satelliteData.length;
          } else {
            console.warn(`批次 ${processedBatches + 1} 没有有效的TLE数据`);
          }
        } else {
          console.warn(`批次 ${processedBatches + 1} 没有获取到TLE数据`);
        }
      } catch (error) {
        console.error(`处理批次 ${processedBatches + 1}/${batches.length} 时出错:`, error);
        // 不要显示每次批处理错误，仅记录日志
      } finally {
        processedBatches++;
        
        // 更新总体进度
        const processedCount = Math.min(processedBatches * batchSize, noradIds.length);
        const progress = Math.round((processedCount / noradIds.length) * 100);
        setTleLoadingProgress(progress);
        
        // 更新同一个进度条消息
        message.loading({ 
          content: `处理卫星数据 (${processedCount}/${noradIds.length}) ${progress}%`, 
          key: messageKey, 
          duration: 0 
        });
      }
      
      // 批次之间延迟一下，让UI有时间呼吸
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // 所有批次处理完成
    console.log(`所有批次处理完成: ${processedBatches}/${batches.length} 批次，共处理 ${totalProcessedSatellites} 颗卫星`);
    
    if (totalProcessedSatellites > 0) {
      message.success({ 
        content: `成功处理 ${totalProcessedSatellites}/${noradIds.length} 颗卫星数据`, 
        key: messageKey,
        duration: 3
      });
    } else {
      message.warning({
        content: '未能成功处理任何卫星数据',
        key: messageKey,
        duration: 3
      });
    }
  };

  // 修改渲染卫星选择下拉框的函数
  const renderSatelliteSelect = () => {
    // 始终根据selectedConstellation判断是否显示
    if (!selectedConstellation) {
      // console.log("未选择星座，不显示卫星选择控件"); // 注释掉频繁的日志
      return null;
    }
    
    console.log(`渲染卫星选择控件，当前状态: loadingSatellites=${loadingSatellites}, 
      loadingTleData=${loadingTleData}, satelliteOptions数量=${satelliteOptions.length}`);
    
    // 如果正在加载卫星数据，显示加载进度条
    if (loadingSatellites) {
      return (
        <SatelliteSelectContainer>
          <LoadingProgress>
            <LoadingText>
              正在加载「{selectedConstellation}」星座的卫星数据...
            </LoadingText>
            <ProgressBar>
              <ProgressFill $progress={loadingProgress} />
            </ProgressBar>
            <LoadingText>
              {loadingProgress}%
            </LoadingText>
          </LoadingProgress>
        </SatelliteSelectContainer>
      );
    }
    
    // 如果正在加载TLE数据，显示加载进度条
    if (loadingTleData) {
      return (
        <SatelliteSelectContainer>
          <LoadingProgress>
            <LoadingText>
              正在获取卫星TLE数据并计算轨道...
            </LoadingText>
            <ProgressBar>
              <ProgressFill $progress={tleLoadingProgress} />
            </ProgressBar>
            <LoadingText>
              {tleLoadingProgress}%
            </LoadingText>
          </LoadingProgress>
        </SatelliteSelectContainer>
      );
    }
    
    // 已加载完成，显示卫星选择界面
    const totalSatellites = satelliteOptions.length;
    const selectedCount = selectedSatellites.length;
    const hasSelection = selectedConstellation || selectedSatellites.length > 0;
    
    console.log(`准备显示卫星选择下拉列表: 总数=${totalSatellites}, 选中=${selectedCount}`);
    
    return (
      <SatelliteSelectContainer>
        <SatelliteSelectLabel>
          <span>选择{selectedConstellation}卫星（{selectedCount}/{totalSatellites}）:</span>
          <div>
            {selectedCount === 0 ? (
              <SelectAllButton onClick={handleSelectAllSatellites}>
                全选
              </SelectAllButton>
            ) : (
              <SelectAllButton onClick={handleUnselectAllSatellites}>
                清除
              </SelectAllButton>
            )}
          </div>
        </SatelliteSelectLabel>
        
        {/* 恢复卫星下拉选择框 */}
        {/* @ts-ignore - 类型不匹配但功能正确 */}
        <StyledSelect
          mode="multiple"
          value={selectedSatellites}
          onChange={(values: string[]) => handleSatelliteSelectionChange(values)}
          options={satelliteOptions}
          loading={loadingSatellites}
          placeholder={loadingSatellites ? "加载中..." : "请选择卫星"}
          style={{ width: '100%', marginBottom: '10px' }}
          maxTagCount={2}
          showSearch
          optionFilterProp="label"
          filterOption={(input: string, option?: any) => {
            // 使用searchText属性进行过滤，它包含卫星名称和NORAD ID
            if (!option || typeof option !== 'object') return false;
            
            if (!option?.searchText) {
              // 如果searchText不存在，则使用label和value进行搜索
              return (option?.label?.toString() || '').toLowerCase().includes(input.toLowerCase()) ||
                     (option?.value?.toString() || '').toLowerCase().includes(input.toLowerCase());
            }
            return option.searchText.toLowerCase().includes(input.toLowerCase());
          }}
          notFoundContent={loadingSatellites ? "加载中..." : "没有找到匹配的卫星"}
          listHeight={200}
        />
        
        <ConfirmButton 
          onClick={handleConfirmSelection}
          disabled={!hasSelection || loadingTleData}
        >
          {loadingTleData ? "处理中..." : "确定"}
        </ConfirmButton>
      </SatelliteSelectContainer>
    );
  };

  const renderSelectAfterTitle = (
    nodeData: DataNode, 
    selectedOptions: Record<string, string[]>,
    onSelectChange: (key: string, values: string[]) => void
  ): React.ReactNode => {
    // 针对星座选择的处理函数
    const handleConstellationSelect = (value: string) => {
      // 当选择星座时，直接使用选中的值
      onSelectChange(nodeData.key as string, value ? [value] : []);
    };

    // 针对其他多选的处理函数
    const handleMultiSelect = (values: string[]) => {
      onSelectChange(nodeData.key as string, values);
    };

    // 根据节点类型选择不同的处理函数
    const handleSelect = nodeData.key === 'constellation' 
      ? handleConstellationSelect 
      : handleMultiSelect;

    const commonProps = {
      // 星座使用单选，其他使用多选
      mode: nodeData.key === 'constellation' ? undefined : "multiple" as const,
      // 星座单选时，值需要是字符串；多选时，值是字符串数组
      value: nodeData.key === 'constellation' 
        ? (selectedOptions[nodeData.key as string]?.[0] || undefined) 
        : selectedOptions[nodeData.key as string],
      // 修复类型问题
      onChange: handleSelect as any,
      onClick: (e: React.MouseEvent) => {
        e.stopPropagation();
        e.preventDefault();
      },
      popupClassName: "custom-select-dropdown",
      maxTagCount: 1,
      size: "middle" as const,
      menuItemSelectedIcon: null,
      virtual: false,
      suffixIcon: null,
    };

    const selectMap = {
      'constellation': {
        placeholder: "请输入或选择星座名称",
        options: constellationOptions,
        loading: loading,
        showSearch: true, // 启用搜索
        filterOption: (input: string, option?: { label: string; value: string }) => 
          (option?.label || '').toLowerCase().indexOf(input.toLowerCase()) >= 0 || 
          (option?.value || '').toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      'launch-site': {
        placeholder: "选择发射场",
        options: launchSiteOptions,
      },
    };

    const config = selectMap[nodeData.key as keyof typeof selectMap];
    if (!config) return null;

    return (
      <>
        <GlobalSelectDropdown />
        <div className="select-container" onClick={e => e.stopPropagation()}>
          <StyledSelect
            {...commonProps}
            {...config}
          />
          {nodeData.key === 'constellation' && renderSatelliteSelect()}
        </div>
      </>
    );
  };

  const renderTitle = (nodeData: DataNode): React.ReactNode => {
    // 处理卫星星座和发射场节点
    if (['constellation', 'launch-site'].includes(nodeData.key as string)) {
      return (
        <TreeNodeContent>
          <span className="node-title">{nodeData.title}</span>
          <SelectContainer $visible={selectedKeys.includes(nodeData.key as string)}>
            {renderSelectAfterTitle(nodeData, selectedOptions, handleSelectChange)}
          </SelectContainer>
        </TreeNodeContent>
      );
    }
    
    // 默认只返回标题
    return nodeData.title;
  };

  // 渲染全部卫星节点标题（只显示卫星总数）
  const renderAllSatellitesTitle = () => {
    const { hasData, satelliteCount, isUpdating } = tleDataStatus;
    
    return (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span>全部卫星</span>
        {hasData && (
          <span 
            style={{ 
              marginLeft: '8px',
              padding: '2px 6px',
              borderRadius: '10px',
              fontSize: '11px',
              fontWeight: '500',
              background: 'linear-gradient(135deg, rgba(64, 169, 255, 0.15), rgba(64, 169, 255, 0.25))',
              border: '1px solid rgba(64, 169, 255, 0.3)',
              color: '#40a9ff',
              backdropFilter: 'blur(4px)',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
              minWidth: '20px',
              textAlign: 'center'
            }}
          >
            {satelliteCount.toLocaleString()}
          </span>
        )}
        </div>
        <Tooltip title="手动更新TLE数据" placement="top">
          <Button
            type="text"
            size="small"
            icon={<ReloadOutlined spin={isUpdating} />}
            loading={isUpdating}
            onClick={(e) => {
              e.stopPropagation(); // 防止触发树节点的点击事件
              handleRefreshTleData();
            }}
            style={{
              color: 'rgba(255, 255, 255, 0.65)',
              border: 'none',
              background: 'transparent',
              padding: '2px 4px',
              height: '20px',
              minWidth: '20px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.color = '#40a9ff';
              e.currentTarget.style.background = 'rgba(64, 169, 255, 0.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.color = 'rgba(255, 255, 255, 0.65)';
              e.currentTarget.style.background = 'transparent';
            }}
          />
        </Tooltip>
      </div>
    );
  };

  const treeData: DataNode[] = [
    {
      title: '空间目标图层',
      key: 'space',
      children: [
        {
          title: '卫星',
          key: 'satellites',
          children: [
            { 
              title: renderAllSatellitesTitle(),
              key: 'all-satellites' 
            },
            { title: '选择星座', key: 'constellation' },
            { title: '选择卫星', key: 'select-satellites' },
          ],
        },
        { title: '空间碎片', key: 'debris' },
      ],
    },
    {
      title: '地面设施图层',
      key: 'ground',
      children: [
        { 
          title: '发射场',
          key: 'launch-site',
        },
      ],
    },
  ];

  const onExpand = (expandedKeysValue: string[]) => {
    setExpandedKeys(expandedKeysValue);
  };

  // 处理卫星视场图层的显示/隐藏
  const handleSatelliteViewChange = (isChecked: boolean) => {
    // 检查是否有选中的卫星星座
    const selectedConstellations = selectedOptions['constellation'] || [];

    if (isChecked && selectedConstellations.length > 0) {
      // 对每个选中的星座显示卫星视场
      selectedConstellations.forEach(constellation => {
        cesiumController?.showSatelliteSensors(constellation);
      });
      console.log('Showing satellite sensors');
    } else {
      // 隐藏所有卫星视场
      selectedConstellations.forEach(constellation => {
        cesiumController?.hideSatelliteSensors(constellation);
      });
      console.log('Hiding satellite sensors');
    }
  };

  const onCheck = (checkedKeysValue: string[]) => {
    console.log('🔔 onCheck函数被调用!');
    console.log('新的选中状态:', checkedKeysValue);
    console.log('之前的选中状态:', checkedKeys);
    
    setCheckedKeys(checkedKeysValue);

    // 处理全部卫星图层的显示/隐藏
    const wasAllSatellitesChecked = checkedKeys.includes('all-satellites');
    const isAllSatellitesChecked = checkedKeysValue.includes('all-satellites');
    
    console.log('全部卫星状态变化:', {
      之前: wasAllSatellitesChecked,
      现在: isAllSatellitesChecked,
      状态改变: wasAllSatellitesChecked !== isAllSatellitesChecked
    });
    
    if (wasAllSatellitesChecked !== isAllSatellitesChecked) {
      if (isAllSatellitesChecked) {
        console.log('🚀 准备调用processAllSatellites函数');
        processAllSatellites();
        console.log('Showing all satellites');
      } else {
        // 隐藏所有卫星
        console.log('🗑️ 隐藏所有卫星');
        if (cesiumController) {
          cesiumController.hideAllSatellites();
        }
        console.log('✅ 已调用hideAllSatellites方法');
      }
    }

    // 处理碎片图层的显示/隐藏
    const wasDebrisChecked = checkedKeys.includes('debris');
    const isDebrisChecked = checkedKeysValue.includes('debris');
    
    if (wasDebrisChecked !== isDebrisChecked) {
      if (isDebrisChecked) {
        cesiumController?.showDebris();
        console.log('Showing debris');
      } else {
        cesiumController?.hideDebris();
        console.log('Hiding debris');
      }
    }

    // 处理发射场图层的显示/隐藏
    const wasLaunchSiteChecked = checkedKeys.includes('launch-site');
    const isLaunchSiteChecked = checkedKeysValue.includes('launch-site');

    if (wasLaunchSiteChecked !== isLaunchSiteChecked) {
      if (isLaunchSiteChecked) {
        cesiumController?.showLaunchSites();
        console.log('Showing launch sites');
      } else {
        cesiumController?.hideLaunchSites();
        console.log('Hiding launch sites');
      }
    }
  };

  const onSelect = (selectedKeysValue: string[]) => {
    setSelectedKeys(selectedKeysValue);
  };

  // 在组件初始化时同步视锥显示状态
  useEffect(() => {
    // 移除了轨道、标签和传感器状态的同步逻辑
  }, [cesiumController]);

  // 恢复 launchSiteOptions 定义
  // 模拟数据 - 发射场数据保持不变
  const launchSiteOptions = [
    { label: '文昌航天发射场', value: 'wenchang' },
    { label: '酒泉卫星发射中心', value: 'jiuquan' },
    { label: '西昌卫星发射中心', value: 'xichang' },
    { label: '太原卫星发射中心', value: 'taiyuan' },
  ];

  // 修改变量名，保持一致性
  useEffect(() => {
    if (checkedKeys && Array.isArray(checkedKeys)) {
      const newCheckedKeysValue = checkedKeys.filter(
        (key): key is string => typeof key === 'string'
      );
      
      // 更新选中的卫星键值
      if (selectedTypeKey === 'satellites') {
        setSelectedSatelliteKeys(newCheckedKeysValue);
      }
    }
  }, [checkedKeys, selectedTypeKey]);

  // 获取所有卫星TLE数据（优先使用缓存）
  const fetchAllSatellitesTle = async (): Promise<TleDataItem[]> => {
    try {
      console.log('🌐 获取所有卫星TLE数据（优先使用缓存）');
      
      setLoadingAllSatellites(true);
      setAllSatellitesProgress(0);
      
      // 使用TLE数据管理器获取数据
      const tleData = await tleDataManager.getTleData();
      
      if (tleData.length > 0) {
        console.log(`✅ 成功获取TLE数据，共 ${tleData.length} 颗卫星`);
        setAllSatellitesProgress(100);
        return tleData;
      } else {
        console.warn('⚠️ 未获取到TLE数据');
        message.warning('未获取到卫星TLE数据，请检查网络连接或稍后重试');
        return [];
      }
    } catch (error) {
      console.error('❌ 获取TLE数据失败:', error);
      message.error('获取卫星TLE数据失败');
      return [];
    } finally {
      setLoadingAllSatellites(false);
      console.log('📋 fetchAllSatellitesTle 函数执行完毕');
    }
  };

  // 手动刷新TLE数据
  const handleRefreshTleData = async () => {
    try {
      console.log('🔄 用户手动刷新TLE数据');
      message.loading({ content: '正在刷新TLE数据...', key: 'refresh-tle', duration: 0 });
      
      // 设置一个超时机制，防止消息一直显示
      const timeoutId = setTimeout(() => {
        console.warn('⚠️ 刷新TLE数据超时，强制清除loading消息');
        message.destroy('refresh-tle');
        message.error({ content: '刷新TLE数据超时，请重试', key: 'refresh-tle', duration: 3 });
      }, 30000); // 30秒超时
      
      console.log('📡 开始调用 tleDataManager.refreshData()');
      const refreshedData = await tleDataManager.refreshData();
      console.log('📡 tleDataManager.refreshData() 返回结果:', refreshedData ? '成功' : '失败');
      
      // 清除超时
      clearTimeout(timeoutId);
      
      // 监听器会处理成功/失败消息，这里只处理后续逻辑
      if (refreshedData) {
        console.log('✅ 刷新成功，数据总数:', refreshedData.total);
        // 如果当前全部卫星图层是选中状态，重新加载显示
        if (checkedKeys.includes('all-satellites')) {
          console.log('🔄 全部卫星图层已选中，重新加载显示');
          message.loading({ content: '正在更新卫星显示...', key: 'refresh-display', duration: 0 });
          
          try {
            // 先隐藏当前显示的卫星
            if (cesiumController) {
              cesiumController.hideAllSatellites();
            }
            
            // 重新处理并显示所有卫星
            await processAllSatellites();
            
            message.success({ content: '卫星显示更新完成', key: 'refresh-display', duration: 2 });
          } catch (displayError) {
            console.error('更新卫星显示失败:', displayError);
            message.error({ content: '更新卫星显示失败', key: 'refresh-display', duration: 3 });
          }
        }
      } else {
        console.warn('⚠️ 刷新失败，refreshedData 为 null');
      }
    } catch (error) {
      console.error('刷新TLE数据失败:', error);

      // 检查是否是存储配额问题
      if (error instanceof Error && (
        error.message.includes('QuotaExceededError') ||
        error.message.includes('quota') ||
        error.message.includes('storage')
      )) {
        console.log('🧹 检测到存储配额问题，尝试清理localStorage...');
        try {
          const { multilayerCacheManager } = await import('../../../utils/multilayerCacheManager');
          await multilayerCacheManager.clearLocalStorageOnly();
          message.warning({
            content: '存储空间不足，已清理缓存，请重试刷新',
            key: 'refresh-tle',
            duration: 5
          });
        } catch (cleanupError) {
          console.error('清理localStorage失败:', cleanupError);
          message.error({
            content: '存储空间不足且清理失败，请手动清理浏览器缓存',
            key: 'refresh-tle',
            duration: 5
          });
        }
      } else {
        // 如果监听器没有处理错误，这里作为备用
        message.error({ content: '刷新TLE数据失败: ' + (error instanceof Error ? error.message : String(error)), key: 'refresh-tle', duration: 3 });
      }
    }
  };

  // 处理并显示所有卫星（快速渲染模式）
  const processAllSatellites = async () => {
    console.log('=== 开始快速渲染全部卫星功能 ===');
    console.log('cesiumController状态:', cesiumController ? '已初始化' : '未初始化');
    
    if (!cesiumController) {
      console.error('❌ 地图控制器未初始化');
      message.error('地图控制器未初始化，请刷新页面重试');
      return;
    }

    // 创建统一的进度条
    const progressKey = 'fast-satellite-loading';
    message.loading({ 
      content: '正在获取卫星TLE数据...', 
      key: progressKey, 
      duration: 0 
    });

    try {
      console.log('🚀 开始获取所有卫星TLE数据...');
      // 获取所有卫星TLE数据
      const allSatellitesTle = await fetchAllSatellitesTle();
      
      console.log('📡 TLE数据获取结果:', {
        获取到的卫星数量: allSatellitesTle.length,
        数据样本: allSatellitesTle.slice(0, 2)
      });
      
      if (allSatellitesTle.length === 0) {
        console.error('❌ 未获取到任何卫星数据');
        message.error('未获取到卫星数据，请检查后端服务是否正常运行');
        return;
      }

      // 更新进度条
      message.loading({ 
        content: `获取到 ${allSatellitesTle.length} 颗卫星数据，正在快速渲染...`, 
        key: progressKey, 
        duration: 0 
      });
      
      console.log('⚡ 开始快速处理TLE数据...');
      
      // 准备卫星数据 - 处理新的数据格式，添加数据验证
      const satelliteData = allSatellitesTle
        .filter(data => isValidTleData(data))
        .map(data => {
          // 新格式的TLE数据已经包含完整的TLE行
          const tleLines = data.tle_raw.split('\n');
          let line1 = '', line2 = '';
          
          // 查找TLE第一行和第二行
          for (const line of tleLines) {
            const trimmedLine = line.trim();
            if (trimmedLine.startsWith('1 ')) {
              line1 = trimmedLine;
            } else if (trimmedLine.startsWith('2 ')) {
              line2 = trimmedLine;
            }
          }
          
          if (!line1 || !line2) {
            console.warn(`⚠️ 无法解析TLE数据: ${data.satellite_name}`);
            return null;
          }
          
          // 验证数据完整性
          if (!data.norad_id || typeof data.norad_id !== 'number') {
            console.warn('⚠️ 无效的norad_id:', data);
            return null;
          }

          return {
            id: data.norad_id.toString(),
            name: data.satellite_name || `Satellite-${data.norad_id}`,
            line1,
            line2,
            constellation: 'All-Satellites'
          };
        })
        .filter((item): item is NonNullable<typeof item> => item !== null);

      if (satelliteData.length === 0) {
        console.error('❌ 没有有效的TLE数据');
        message.error('TLE数据解析失败，无法显示卫星');
        return;
      }

      console.log(`✅ 成功处理 ${satelliteData.length} 颗卫星的TLE数据`);
      
      // 使用混合渲染器（3D Tiles优先，自动降级到快速渲染）
      await cesiumController.hybridRenderSatellites(satelliteData);
      
      // 获取渲染状态信息
      const renderStatus = cesiumController.hybridSatelliteRenderer.getStatus();
      const renderMode = renderStatus.currentMode.type;
      const renderReason = renderStatus.currentMode.reason;
      
      // 渲染完成 - 移除成功提示，静默完成
      message.destroy(progressKey);
      
    } catch (error) {
      console.error('❌ 快速渲染所有卫星时发生错误:', error);
      message.error('快速渲染失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      console.log('=== 快速渲染全部卫星功能处理结束 ===');
    }
  };

  return (
    <PanelContainer $visible={visible}>
      {/* @ts-ignore - 类型不匹配但功能正确 */}
      <StyledTree
        checkable
        onExpand={onExpand}
        expandedKeys={expandedKeys}
        onCheck={onCheck}
        onSelect={onSelect}
        checkedKeys={checkedKeys}
        selectedKeys={selectedKeys}
        treeData={treeData}
        showLine={{ showLeafIcon: false }}
        titleRender={renderTitle}
      />
    </PanelContainer>
  );
} 

// 添加检查坐标有效性的辅助函数
function isValidCoordinate(value: any): boolean {
  return typeof value === 'number' && !isNaN(value) && isFinite(value);
}

// 添加TLE数据验证函数
function isValidTleData(data: any): data is TleDataItem {
  if (!data || typeof data !== 'object') {
    return false;
  }

  // 检查必要字段
  if (!data.norad_id || typeof data.norad_id !== 'number') {
    console.warn('⚠️ TLE数据缺少有效的norad_id:', data);
    return false;
  }

  if (!data.tle_raw || typeof data.tle_raw !== 'string') {
    console.warn('⚠️ TLE数据缺少有效的tle_raw:', data);
    return false;
  }

  // 检查TLE格式
  const tleLines = data.tle_raw.split('\n');
  let hasLine1 = false, hasLine2 = false;

  for (const line of tleLines) {
    const trimmedLine = line.trim();
    if (trimmedLine.startsWith('1 ')) {
      hasLine1 = true;
    } else if (trimmedLine.startsWith('2 ')) {
      hasLine2 = true;
    }
  }

  if (!hasLine1 || !hasLine2) {
    console.warn('⚠️ TLE数据格式无效，缺少第一行或第二行:', data);
    return false;
  }

  return true;
}